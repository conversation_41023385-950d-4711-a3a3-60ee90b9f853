package config

import "fmt"

type MongoConfig struct {
	Hosts      string
	User       string
	Password   string
	Database   string
	AuthSource string
	ReplicaSet string
	AppName    string
	Timeout    TimeoutConfig
}

type TimeoutConfig struct {
	MongoWriteDefault int
}

func (c *Config) MongoURI() string {
	// Handle case where no authentication is required
	if c.Mongo.User == "" || c.Mongo.Password == "" {
		// For localhost development without replica set
		if c.Mongo.ReplicaSet == "" {
			return fmt.Sprintf(
				"mongodb://%s/%s?appName=%s",
				c.Mongo.Hosts,
				c.Mongo.Database,
				c.Mongo.AppName,
			)
		}
		// For replica set without authentication
		return fmt.Sprintf(
			"mongodb://%s/%s?readPreference=secondaryPreferred&replicaSet=%s&appName=%s",
			c.Mongo.Hosts,
			c.Mongo.Database,
			c.Mongo.ReplicaSet,
			c.Mongo.AppName,
		)
	}

	// Handle case with authentication
	authSourceParam := ""
	if c.Mongo.AuthSource != "" {
		authSourceParam = fmt.Sprintf("&authSource=%s", c.Mongo.AuthSource)
	}

	return fmt.Sprintf(
		"mongodb://%s:%s@%s/%s?readPreference=secondaryPreferred&replicaSet=%s&appName=%s%s",
		c.Mongo.User,
		c.Mongo.Password,
		c.Mongo.Hosts,
		c.Mongo.Database,
		c.Mongo.ReplicaSet,
		c.Mongo.AppName,
		authSourceParam,
	)
}
