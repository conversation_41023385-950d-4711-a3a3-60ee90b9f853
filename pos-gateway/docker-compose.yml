services:
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    environment:
      <PERSON><PERSON><PERSON><PERSON>PER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"
    networks:
      - pos-gateway-network

  kafka:
    image: confluentinc/cp-kafka:latest
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"
    networks:
      - pos-gateway-network

  mongodb:
    image: mongo:7.0
    container_name: pos-gateway-mongodb
    restart: always
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_DATABASE: restaurants
    volumes:
      - mongodb_data:/data/db
    networks:
      - pos-gateway-network

volumes:
  mongodb_data:

networks:
  pos-gateway-network:
    driver: bridge